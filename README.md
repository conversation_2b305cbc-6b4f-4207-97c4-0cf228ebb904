# Qwen3 Token统计显示修复

## 问题描述
原始代码在模型回复触及底部时，会出现输出混乱的问题：
- 状态栏与正常内容混合显示
- Token统计信息被截断或重叠
- 终端滚动时状态栏位置不固定

## 解决方案

### 1. 设置滚动区域
使用ANSI转义序列设置滚动区域，为状态栏保留最后一行：
```python
# 设置滚动区域为第1行到倒数第2行
sys.stdout.write(f'\033[1;{terminal_size.lines-1}r')
```

### 2. 改进状态栏显示函数
- 使用更安全的光标保存/恢复机制
- 确保状态栏始终显示在最后一行，不受滚动区域影响
- 添加反色显示效果以突出状态栏

### 3. 实时Token统计
- 使用`tiktoken`库进行准确的token计数
- 分别统计Thinking和Answer阶段的tokens
- 实时更新总token数量

## 主要文件

### `Qwen3Sample.py`
主要的Qwen3模型调用脚本，包含完整的token统计和状态栏显示功能。

### `test_token_display.py`
简化版本的测试脚本，使用较短的提示词以减少测试时间。

### `demo_token_display.py`
演示脚本，使用模拟数据展示token统计功能，无需实际调用API。

## 功能特性

1. **实时Token统计**：在Thinking阶段实时显示token数量
2. **分阶段统计**：分别显示Thinking和Answer阶段的token数量
3. **固定状态栏**：状态栏始终显示在屏幕底部，不会被内容覆盖
4. **滚动兼容**：内容滚动时状态栏保持固定位置
5. **最终统计**：程序结束时显示完整的token统计信息

## 使用方法

### 安装依赖
```bash
pip install tiktoken openai
```

### 运行主程序
```bash
python3 Qwen3Sample.py
```

### 运行演示
```bash
python3 demo_token_display.py
```

## 技术细节

### ANSI转义序列
- `\033[s` / `\033[u`：保存/恢复光标位置
- `\033[{line};{col}H`：移动光标到指定位置
- `\033[K`：清除从光标到行末的内容
- `\033[7m` / `\033[0m`：反色显示/重置格式
- `\033[{start};{end}r`：设置滚动区域

### Token计数
使用`tiktoken`库的`cl100k_base`编码器进行token计数，与GPT-4兼容。

### 终端兼容性
代码包含终端尺寸检测和默认值设置，确保在不同环境下的兼容性。

## 修复效果

修复前：
```
### 核心能力
Tokens - Thinking: 545 | Answer: 181 | Total: 726                                                                                                              
Tokens - 的推理能力**  | Answer: 189 | Total: 734                                                                                                              
```

修复后：
```
### 核心能力
1. **更强更全的基座模型**
   基于更全面的知识语料，我拥有更丰富的语言理解和表达能力
Tokens - Thinking: 545 | Answer: 181 | Total: 726
```

状态栏现在始终保持在底部，内容显示清晰无混乱。
