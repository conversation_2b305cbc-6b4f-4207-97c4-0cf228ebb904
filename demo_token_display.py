#!/usr/bin/env python3
import sys
import os
import time

# 获取终端尺寸
def get_terminal_size():
    try:
        return os.get_terminal_size()
    except:
        return os.terminal_size((80, 24))  # 默认尺寸

# 清除状态栏并重置滚动区域
def clear_status_line():
    terminal_size = get_terminal_size()
    # 移动到最后一行，清除该行
    sys.stdout.write(f'\033[{terminal_size.lines};1H\033[K')
    # 重置滚动区域到整个屏幕
    sys.stdout.write(f'\033[1;{terminal_size.lines}r')
    sys.stdout.flush()

# 显示token统计信息
def show_token_stats(reasoning_tokens, answer_tokens, total_tokens):
    terminal_size = get_terminal_size()
    status_text = f"Tokens - Thinking: {reasoning_tokens} | Answer: {answer_tokens} | Total: {total_tokens}"
    
    # 如果状态文本太长，截断它
    if len(status_text) > terminal_size.columns - 2:
        status_text = status_text[:terminal_size.columns - 5] + "..."
    
    # 保存当前光标位置
    sys.stdout.write('\033[s')
    
    # 移动到状态栏位置（最后一行），不受滚动区域影响
    sys.stdout.write(f'\033[{terminal_size.lines};1H')
    sys.stdout.write('\033[K')  # 清除状态行
    sys.stdout.write(f'\033[7m{status_text.ljust(terminal_size.columns)}\033[0m')
    
    # 恢复光标位置
    sys.stdout.write('\033[u')
    sys.stdout.flush()

def main():
    # 设置滚动区域，为状态栏保留最后一行
    terminal_size = get_terminal_size()
    print('\033[2J\033[H')  # 清屏并移动到顶部
    # 设置滚动区域为第1行到倒数第2行
    sys.stdout.write(f'\033[1;{terminal_size.lines-1}r')
    sys.stdout.write('\033[H')  # 移动到顶部
    sys.stdout.flush()
    
    print("演示Token统计显示功能")
    print("=" * 50)
    print()
    
    # 模拟Thinking阶段
    thinking_text = """这是一个模拟的思考过程。我正在思考如何回答用户的问题。
首先，我需要理解用户的需求。
然后，我需要组织我的回答。
接下来，我会考虑最佳的表达方式。
最后，我会生成一个完整的回答。
这个过程需要仔细考虑每个细节。
我要确保回答既准确又有用。
同时，我也要保持回答的简洁性。
让我继续思考更多的内容来演示滚动效果。
这里是更多的思考内容，用来测试当内容超过屏幕高度时的显示效果。
我们需要确保状态栏始终固定在底部，不会被内容覆盖。
即使内容滚动，状态栏也应该保持可见。
这是一个重要的用户体验改进。
现在让我们看看这个修复是否有效。"""
    
    reasoning_tokens = 0
    for i, char in enumerate(thinking_text):
        if char != '\n':
            reasoning_tokens += 1
        print(char, end='', flush=True)
        show_token_stats(reasoning_tokens, 0, reasoning_tokens)
        time.sleep(0.05)  # 模拟实时输出
    
    print("\n\n === Final Answer ===\n")
    
    # 模拟Answer阶段
    answer_text = """你好！我是一个AI助手，专门设计来帮助用户解决各种问题。
我具有以下能力：
1. 回答问题和提供信息
2. 协助完成各种任务
3. 进行创意写作
4. 代码编程和调试
5. 语言翻译
6. 数据分析

我很高兴为您提供帮助！如果您有任何问题或需要协助，请随时告诉我。
我会尽我所能为您提供准确、有用的回答。"""
    
    answer_tokens = 0
    for i, char in enumerate(answer_text):
        if char != '\n':
            answer_tokens += 1
        print(char, end='', flush=True)
        show_token_stats(reasoning_tokens, answer_tokens, reasoning_tokens + answer_tokens)
        time.sleep(0.03)  # 模拟实时输出
    
    # 清除状态栏
    clear_status_line()
    print(f"\n\n最终统计 - Thinking Tokens: {reasoning_tokens}, Answer Tokens: {answer_tokens}, Total: {reasoning_tokens + answer_tokens}")
    print("\n演示完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        clear_status_line()
        print("\n\n演示被用户中断")
