from openai import OpenAI
import tiktoken
import sys
import os

# 获取终端尺寸
def get_terminal_size():
    try:
        return os.get_terminal_size()
    except:
        return os.terminal_size((80, 24))  # 默认尺寸

# 清除状态栏并重置滚动区域
def clear_status_line():
    terminal_size = get_terminal_size()
    # 移动到最后一行，清除该行
    sys.stdout.write(f'\033[{terminal_size.lines};1H\033[K')
    # 重置滚动区域到整个屏幕
    sys.stdout.write(f'\033[1;{terminal_size.lines}r')
    sys.stdout.flush()

# 显示token统计信息
def show_token_stats(reasoning_tokens, answer_tokens, total_tokens):
    terminal_size = get_terminal_size()
    status_text = f"Tokens - Thinking: {reasoning_tokens} | Answer: {answer_tokens} | Total: {total_tokens}"

    # 如果状态文本太长，截断它
    if len(status_text) > terminal_size.columns - 2:
        status_text = status_text[:terminal_size.columns - 5] + "..."

    # 保存当前光标位置
    sys.stdout.write('\033[s')

    # 移动到状态栏位置（最后一行），不受滚动区域影响
    sys.stdout.write(f'\033[{terminal_size.lines};1H')
    sys.stdout.write('\033[K')  # 清除状态行
    sys.stdout.write(f'\033[7m{status_text.ljust(terminal_size.columns)}\033[0m')

    # 恢复光标位置
    sys.stdout.write('\033[u')
    sys.stdout.flush()

# 初始化tiktoken编码器
try:
    encoding = tiktoken.encoding_for_model("gpt-4")
except:
    encoding = tiktoken.get_encoding("cl100k_base")

client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1',
    api_key='ms-79f6072b-bcc8-45dc-8b03-76017769de5c', # ModelScope Token
)

# 设置滚动区域，为状态栏保留最后一行
terminal_size = get_terminal_size()
print('\033[2J\033[H')  # 清屏并移动到顶部
# 设置滚动区域为第1行到倒数第2行
sys.stdout.write(f'\033[1;{terminal_size.lines-1}r')
sys.stdout.write('\033[H')  # 移动到顶部
sys.stdout.flush()

response = client.chat.completions.create(
    model='Qwen/Qwen3-235B-A22B-Thinking-2507', # ModelScope Model-Id
    messages=[
        {
            'role': 'user',
            'content': '简单介绍一下你自己，不超过100字'  # 简化问题以减少响应时间
        }
    ],
    stream=True
)

done_reasoning = False
reasoning_content = ""
answer_content = ""
reasoning_tokens = 0
answer_tokens = 0
current_line = 1

try:
    for chunk in response:
        reasoning_chunk = chunk.choices[0].delta.reasoning_content
        answer_chunk = chunk.choices[0].delta.content
        
        if reasoning_chunk != '':
            reasoning_content += reasoning_chunk
            reasoning_tokens = len(encoding.encode(reasoning_content))
            print(reasoning_chunk, end='', flush=True)
            
            # 实时更新token统计
            total_tokens = reasoning_tokens + answer_tokens
            show_token_stats(reasoning_tokens, answer_tokens, total_tokens)
            
        elif answer_chunk != '':
            if not done_reasoning:
                print('\n\n === Final Answer ===\n')
                done_reasoning = True
            
            answer_content += answer_chunk
            answer_tokens = len(encoding.encode(answer_content))
            print(answer_chunk, end='', flush=True)
            
            # 实时更新token统计
            total_tokens = reasoning_tokens + answer_tokens
            show_token_stats(reasoning_tokens, answer_tokens, total_tokens)

except KeyboardInterrupt:
    print("\n\n程序被用户中断")

finally:
    # 清除状态栏
    clear_status_line()
    print(f"\n\n最终统计 - Thinking Tokens: {reasoning_tokens}, Answer Tokens: {answer_tokens}, Total: {reasoning_tokens + answer_tokens}")
